import React from 'react';

interface ApprovalLogProps {
  data: {
    action: string;
    approver: string;
    date: string;
    comments: string;
  }[];
}

const ApprovalLog: React.FC<ApprovalLogProps> = ({ data }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-4 sm:p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Approval Log</h2>
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {data.map((log, index) => (
            <div key={index} className="border-l-4 border-teal-500 pl-4 py-2">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{log.action}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    by <span className="font-medium">{log.approver}</span>
                  </p>
                  {log.comments && (
                    <p className="text-xs text-gray-600 mt-1 italic">"{log.comments}"</p>
                  )}
                </div>
                <div className="text-xs text-gray-500 whitespace-nowrap">
                  {log.date}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ApprovalLog;
