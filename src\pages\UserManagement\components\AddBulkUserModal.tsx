import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import Modal from '../../../components/Modal';
import { Button } from '../../../components/Button';
import { Download } from 'lucide-react';

interface AddBulkUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (file: File) => void;
}

const AddBulkUserModal: React.FC<AddBulkUserModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      setSelectedFile(acceptedFiles[0]);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
    },
    maxFiles: 1,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedFile) {
      onSubmit(selectedFile);
      onClose();
      setSelectedFile(null);
    }
  };

  const handleDownloadSample = () => {
    // This would typically generate and download a sample CSV file
    // For now, we'll create a simple CSV string
    const csvContent = 
      "first_name,last_name,email,department,role,manager_email\n" +
      "John,Doe,<EMAIL>,Finance,Employee,<EMAIL>\n" +
      "Jane,Smith,<EMAIL>,Marketing,Manager,<EMAIL>";
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'sample_users.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add Bulk User"
      className="max-w-3xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-gray-600">
          <p className="text-sm">
            Import employee data from a CSV file. Ensure your file includes columns for employee name, email, department, and role. Download a sample CSV to see the required format.
          </p>
        </div>

        <div className="flex justify-end">
          <Button
            type="button"
            onClick={handleDownloadSample}
            className="bg-teal-700 hover:bg-teal-800 text-white px-4 py-2 rounded-md flex items-center gap-2"
          >
            <Download size={16} />
            Download Sample CSV
          </Button>
        </div>

        <div 
          {...getRootProps()} 
          className={`border-2 border-dashed rounded-lg p-10 text-center cursor-pointer transition-colors ${
            isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
          } ${selectedFile ? 'bg-green-50 border-green-300' : ''}`}
        >
          <input {...getInputProps()} />
          
          <div className="flex flex-col items-center justify-center space-y-4">
            <p className="text-lg font-medium text-gray-700">
              {selectedFile 
                ? `Selected: ${selectedFile.name}` 
                : 'Drag and drop a CSV file here'}
            </p>
            <p className="text-sm text-gray-500">Or click to browse</p>
            
            {!selectedFile && (
              <Button
                type="button"
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md mt-2"
              >
                Select .CSV File
              </Button>
            )}
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button
            type="submit"
            disabled={!selectedFile}
            className={`px-8 py-3 rounded-md font-medium ${
              selectedFile 
                ? 'bg-teal-700 hover:bg-teal-800 text-white' 
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Submit
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddBulkUserModal;