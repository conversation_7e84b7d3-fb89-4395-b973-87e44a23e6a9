import React from 'react';

interface ReportDetailProps {
  data: {
    reportNumber: string;
    submittedOn: string;
    approvedOn: string;
    status: string;
  };
}

const ReportDetail: React.FC<ReportDetailProps> = ({ data }) => {
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium';
      case 'rejected':
        return 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium';
      default:
        return 'bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Report Detail</h3>
      <div className="space-y-3">
        <div>
          <label className="text-sm font-medium text-gray-500">Report Number</label>
          <p className="text-sm text-gray-900 font-medium">{data.reportNumber}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Submitted On</label>
          <p className="text-sm text-gray-900">{data.submittedOn}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Approved On</label>
          <p className="text-sm text-gray-900">{data.approvedOn}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Status</label>
          <div className="mt-1">
            <span className={getStatusBadgeClass(data.status)}>
              {data.status}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportDetail;
