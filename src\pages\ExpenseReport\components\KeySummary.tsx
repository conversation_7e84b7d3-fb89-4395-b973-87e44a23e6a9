import React from 'react';

interface KeySummaryProps {
  data: {
    totalExpenses: number;
    totalReimbursement: number;
    outOfPocket: number;
  };
}

const KeySummary: React.FC<KeySummaryProps> = ({ data }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Summary</h3>
      <div className="space-y-3">
        <div>
          <label className="text-sm font-medium text-gray-500">Total Expenses</label>
          <p className="text-lg font-bold text-gray-900">{formatCurrency(data.totalExpenses)}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Total Reimbursement</label>
          <p className="text-lg font-bold text-green-600">{formatCurrency(data.totalReimbursement)}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Out of Pocket</label>
          <p className="text-lg font-bold text-gray-900">{formatCurrency(data.outOfPocket)}</p>
        </div>
      </div>
    </div>
  );
};

export default KeySummary;
