import React from 'react';

interface DisputesAndExceptionsProps {
  data: {
    date: string;
    expense: string;
    amount: number;
    disputeStatus: string;
    description: string;
  }[];
}

const DisputesAndExceptions: React.FC<DisputesAndExceptionsProps> = ({ data }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'resolved':
        return 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium';
      case 'rejected':
        return 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium';
      default:
        return 'bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-4 sm:p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Disputes & Exceptions</h2>
        {data.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Date</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Expense</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Amount</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Dispute Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Description</th>
                </tr>
              </thead>
              <tbody>
                {data.map((dispute, index) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4 text-sm text-gray-900">{dispute.date}</td>
                    <td className="py-4 px-4 text-sm text-gray-900 font-medium">{dispute.expense}</td>
                    <td className="py-4 px-4 text-sm text-gray-900 font-medium">
                      {formatCurrency(dispute.amount)}
                    </td>
                    <td className="py-4 px-4">
                      <span className={getStatusBadgeClass(dispute.disputeStatus)}>
                        {dispute.disputeStatus}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-sm text-gray-600">{dispute.description}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 text-sm">No disputes or exceptions found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DisputesAndExceptions;
