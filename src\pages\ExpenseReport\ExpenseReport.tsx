import React from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { Button } from '../../components/Button';
import { FiDownload } from 'react-icons/fi';
import { employeeExpenseReportData } from '../../mockData/expenseReportData';
import EmployeeDetails from './components/EmployeeDetails';
import KeySummary from './components/KeySummary';
import ReportDetail from './components/ReportDetail';
import DetailedExpenseTable from './components/DetailedExpenseTable';
import CategoryBreakdown from './components/CategoryBreakdown';
import DisputesAndExceptions from './components/DisputesAndExceptions';
import ApprovalLog from './components/ApprovalLog';


const ExpenseReport: React.FC = () => {
  const handleDownload = () => {
    alert("Downloading report...");
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Employee Expense Report</h1>
          <Breadcrumb pageName="Employee Expense Report" />
        </div>
        <Button
          className="brand-gradient px-4 py-3 rounded-md text-sm text-white"
          onClick={handleDownload}
        >
          <FiDownload className="w-4 h-4" />
          Download
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <EmployeeDetails data={employeeExpenseReportData.employeeDetails} />
        <KeySummary data={employeeExpenseReportData.keySummary} />
        <ReportDetail data={employeeExpenseReportData.reportDetail} />
      </div>

      <div className="mb-6">
        <DetailedExpenseTable data={employeeExpenseReportData.detailedExpenseTable} />
      </div>

      <div className="mb-6">
        <CategoryBreakdown data={employeeExpenseReportData.categoryBreakdown} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <DisputesAndExceptions data={employeeExpenseReportData.disputesAndExceptions} />
        <ApprovalLog data={employeeExpenseReportData.approvalLog} />
      </div>
    </div>
  );
};

export default ExpenseReport;
