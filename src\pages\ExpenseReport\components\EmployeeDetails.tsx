import React from 'react';

interface EmployeeDetailsProps {
  data: {
    name: string;
    employeeId: string;
    department: string;
    reportPeriod: string;
  };
}

const EmployeeDetails: React.FC<EmployeeDetailsProps> = ({ data }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Employee Details</h3>
      <div className="space-y-3">
        <div>
          <label className="text-sm font-medium text-gray-500">Employee Name</label>
          <p className="text-sm text-gray-900 font-medium">{data.name}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Employee ID</label>
          <p className="text-sm text-gray-900">{data.employeeId}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Department</label>
          <p className="text-sm text-gray-900">{data.department}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Report Period</label>
          <p className="text-sm text-gray-900">{data.reportPeriod}</p>
        </div>
      </div>
    </div>
  );
};

export default EmployeeDetails;
